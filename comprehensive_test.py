#!/usr/bin/env python3
import socket
import struct
import time
import random
import sys

class DNSTestClient:
    def __init__(self, server_ip="127.0.0.1", server_port=2053):
        self.server_ip = server_ip
        self.server_port = server_port
    
    def create_dns_query(self, domains, query_id=None):
        """Create a DNS query with one or more questions"""
        if query_id is None:
            query_id = random.randint(0x1000, 0xFFFF)
        
        if isinstance(domains, str):
            domains = [domains]
        
        # Header: ID, Flags, QDCOUNT, ANCOUNT, NSCOUNT, ARCOUNT
        header = struct.pack('!HHHHHH', query_id, 0x0100, len(domains), 0, 0, 0)
        
        # Question section
        questions = b''
        for domain in domains:
            question = b''
            for label in domain.split('.'):
                question += struct.pack('!B', len(label)) + label.encode()
            question += b'\x00'  # End of domain name
            question += struct.pack('!HH', 1, 1)  # Type A, Class IN
            questions += question
        
        return header + questions, query_id
    
    def send_dns_query(self, domains, timeout=10):
        """Send DNS query and return response with timing"""
        query, query_id = self.create_dns_query(domains)
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(timeout)
        
        start_time = time.time()
        try:
            sock.sendto(query, (self.server_ip, self.server_port))
            response, addr = sock.recvfrom(2048)
            end_time = time.time()
            return response, query_id, end_time - start_time
        finally:
            sock.close()
    
    def parse_dns_response(self, response):
        """Parse DNS response and extract key information"""
        if len(response) < 12:
            return {"error": "Invalid response - too short"}
        
        # Parse header
        header = struct.unpack('!HHHHHH', response[:12])
        query_id, flags, qdcount, ancount, nscount, arcount = header
        
        qr = (flags >> 15) & 1
        opcode = (flags >> 11) & 0xF
        aa = (flags >> 10) & 1
        tc = (flags >> 9) & 1
        rd = (flags >> 8) & 1
        ra = (flags >> 7) & 1
        rcode = flags & 0xF
        
        return {
            "query_id": query_id,
            "is_response": qr == 1,
            "opcode": opcode,
            "authoritative": aa == 1,
            "truncated": tc == 1,
            "recursion_desired": rd == 1,
            "recursion_available": ra == 1,
            "response_code": rcode,
            "question_count": qdcount,
            "answer_count": ancount,
            "authority_count": nscount,
            "additional_count": arcount,
            "total_length": len(response)
        }
    
    def test_query(self, domains, expected_id=None, description=""):
        """Test a single DNS query and return results"""
        try:
            if isinstance(domains, str):
                domains = [domains]
            
            print(f"\n🧪 Testing: {description}")
            print(f"   Querying: {', '.join(domains)}")
            
            response, sent_id, response_time = self.send_dns_query(domains)
            parsed = self.parse_dns_response(response)
            
            # Verify packet ID matches
            id_match = parsed["query_id"] == (expected_id or sent_id)
            
            print(f"   ✅ Response received in {response_time:.3f}s")
            print(f"   📊 ID: {parsed['query_id']:04x} (sent: {sent_id:04x}) - {'✅' if id_match else '❌'}")
            print(f"   📊 Questions: {parsed['question_count']}, Answers: {parsed['answer_count']}")
            print(f"   📊 RCODE: {parsed['response_code']} ({'✅ Success' if parsed['response_code'] == 0 else '❌ Error'})")
            
            return {
                "success": True,
                "id_preserved": id_match,
                "response_time": response_time,
                "parsed": parsed,
                "domains": domains
            }
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return {"success": False, "error": str(e), "domains": domains}

def main():
    print("🚀 DNS Server Comprehensive Test Suite")
    print("=" * 50)
    
    client = DNSTestClient()
    results = []
    
    # Test 1: Single question queries
    print("\n📋 TEST CATEGORY: Single Question Queries")
    test_cases = [
        ("google.com", "Popular domain"),
        ("github.com", "Tech domain"),
        ("stackoverflow.com", "Long domain name"),
        ("example.org", "Example domain"),
        ("nonexistent-domain-12345.com", "Non-existent domain")
    ]
    
    for domain, desc in test_cases:
        result = client.test_query(domain, description=f"Single query - {desc}")
        results.append(result)
        time.sleep(0.5)  # Small delay between tests
    
    # Test 2: Multi-question queries
    print("\n📋 TEST CATEGORY: Multi-Question Queries")
    multi_test_cases = [
        (["google.com", "github.com"], "Two popular domains"),
        (["example.com", "example.org", "example.net"], "Three example domains"),
        (["short.com", "very-long-domain-name-for-testing.com"], "Mixed length domains"),
    ]
    
    for domains, desc in multi_test_cases:
        result = client.test_query(domains, description=f"Multi-query - {desc}")
        results.append(result)
        time.sleep(1)  # Longer delay for multi-question tests
    
    # Test 3: Packet ID preservation
    print("\n📋 TEST CATEGORY: Packet ID Preservation")
    specific_ids = [0x1234, 0xABCD, 0x0001, 0xFFFF]
    for test_id in specific_ids:
        try:
            query, _ = client.create_dns_query("test-id.com", test_id)
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)
            sock.sendto(query, (client.server_ip, client.server_port))
            response, _ = sock.recvfrom(1024)
            sock.close()
            
            parsed = client.parse_dns_response(response)
            id_match = parsed["query_id"] == test_id
            status = "✅ Preserved" if id_match else f"❌ Changed to {parsed['query_id']:04x}"
            print(f"   🆔 ID {test_id:04x}: {status}")
            
            results.append({
                "success": True,
                "id_preserved": id_match,
                "test_type": "id_preservation",
                "sent_id": test_id,
                "received_id": parsed["query_id"]
            })
        except Exception as e:
            print(f"   🆔 ID {test_id:04x}: ❌ Error - {e}")
            results.append({"success": False, "error": str(e), "test_type": "id_preservation"})
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 50)
    
    successful_tests = [r for r in results if r.get("success", False)]
    failed_tests = [r for r in results if not r.get("success", False)]
    id_preserved_tests = [r for r in successful_tests if r.get("id_preserved", False)]
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"🆔 ID preserved: {len(id_preserved_tests)}/{len(successful_tests)}")
    print(f"❌ Failed tests: {len(failed_tests)}")
    
    if failed_tests:
        print("\n❌ Failed Test Details:")
        for i, test in enumerate(failed_tests, 1):
            print(f"   {i}. {test.get('domains', 'Unknown')}: {test.get('error', 'Unknown error')}")
    
    # Performance stats
    response_times = [r.get("response_time", 0) for r in successful_tests if "response_time" in r]
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        max_time = max(response_times)
        min_time = min(response_times)
        print(f"\n⏱️  Response Times: Avg: {avg_time:.3f}s, Min: {min_time:.3f}s, Max: {max_time:.3f}s")
    
    return len(failed_tests) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
