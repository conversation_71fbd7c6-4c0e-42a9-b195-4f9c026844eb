#!/usr/bin/env python3
import socket
import struct
import time

def test_malformed_packets():
    print("🧪 Testing Edge Cases and Error Handling")
    print("=" * 50)
    
    server_ip = "127.0.0.1"
    server_port = 2053
    
    # Test 1: Empty packet
    print("\n🔍 Test 1: Empty packet")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2)
        sock.sendto(b'', (server_ip, server_port))
        try:
            response, addr = sock.recvfrom(512)
            print("   ❌ Server responded to empty packet (should ignore)")
        except socket.timeout:
            print("   ✅ Server correctly ignored empty packet")
        sock.close()
    except Exception as e:
        print(f"   ⚠️  Exception: {e}")
    
    # Test 2: Malformed header (too short)
    print("\n🔍 Test 2: Malformed header (too short)")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2)
        sock.sendto(b'\x12\x34\x01\x00', (server_ip, server_port))  # Only 4 bytes
        try:
            response, addr = sock.recvfrom(512)
            print("   ❌ Server responded to malformed header")
        except socket.timeout:
            print("   ✅ Server correctly ignored malformed header")
        sock.close()
    except Exception as e:
        print(f"   ⚠️  Exception: {e}")
    
    # Test 3: Zero questions
    print("\n🔍 Test 3: Zero questions")
    try:
        # Valid header but 0 questions
        header = struct.pack('!HHHHHH', 0x1234, 0x0100, 0, 0, 0, 0)
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2)
        sock.sendto(header, (server_ip, server_port))
        try:
            response, addr = sock.recvfrom(512)
            print("   ❌ Server responded to zero questions")
        except socket.timeout:
            print("   ✅ Server correctly ignored zero questions")
        sock.close()
    except Exception as e:
        print(f"   ⚠️  Exception: {e}")
    
    # Test 4: Very long domain name
    print("\n🔍 Test 4: Very long domain name")
    try:
        long_domain = "a" * 60 + ".com"  # 64 chars total
        query_id = 0x4321
        header = struct.pack('!HHHHHH', query_id, 0x0100, 1, 0, 0, 0)
        
        question = b''
        for label in long_domain.split('.'):
            question += struct.pack('!B', len(label)) + label.encode()
        question += b'\x00' + struct.pack('!HH', 1, 1)
        
        query = header + question
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        sock.sendto(query, (server_ip, server_port))
        response, addr = sock.recvfrom(512)
        sock.close()
        
        # Parse response
        resp_header = struct.unpack('!HHHHHH', response[:12])
        resp_id, flags, qdcount, ancount = resp_header[:4]
        
        if resp_id == query_id and ancount > 0:
            print("   ✅ Server handled long domain name correctly")
        else:
            print(f"   ❌ Unexpected response: ID={resp_id:04x}, answers={ancount}")
            
    except Exception as e:
        print(f"   ⚠️  Exception: {e}")
    
    # Test 5: Invalid domain characters
    print("\n🔍 Test 5: Domain with special characters")
    try:
        special_domain = "test-domain_123.example.com"
        query_id = 0x5432
        header = struct.pack('!HHHHHH', query_id, 0x0100, 1, 0, 0, 0)
        
        question = b''
        for label in special_domain.split('.'):
            question += struct.pack('!B', len(label)) + label.encode()
        question += b'\x00' + struct.pack('!HH', 1, 1)
        
        query = header + question
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        sock.sendto(query, (server_ip, server_port))
        response, addr = sock.recvfrom(512)
        sock.close()
        
        resp_header = struct.unpack('!HHHHHH', response[:12])
        resp_id = resp_header[0]
        
        if resp_id == query_id:
            print("   ✅ Server handled special characters correctly")
        else:
            print(f"   ❌ ID mismatch: expected {query_id:04x}, got {resp_id:04x}")
            
    except Exception as e:
        print(f"   ⚠️  Exception: {e}")
    
    # Test 6: Maximum questions (stress test)
    print("\n🔍 Test 6: Many questions (stress test)")
    try:
        num_questions = 10
        query_id = 0x6543
        header = struct.pack('!HHHHHH', query_id, 0x0100, num_questions, 0, 0, 0)
        
        questions = b''
        for i in range(num_questions):
            domain = f"test{i}.example.com"
            question = b''
            for label in domain.split('.'):
                question += struct.pack('!B', len(label)) + label.encode()
            question += b'\x00' + struct.pack('!HH', 1, 1)
            questions += question
        
        query = header + questions
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(10)  # Longer timeout for stress test
        start_time = time.time()
        sock.sendto(query, (server_ip, server_port))
        response, addr = sock.recvfrom(2048)  # Larger buffer
        end_time = time.time()
        sock.close()
        
        resp_header = struct.unpack('!HHHHHH', response[:12])
        resp_id, flags, qdcount, ancount = resp_header[:4]
        
        print(f"   ✅ Stress test completed in {end_time - start_time:.3f}s")
        print(f"   📊 Questions: {qdcount}, Answers: {ancount}")
        print(f"   📊 ID preserved: {resp_id == query_id}")
        
    except Exception as e:
        print(f"   ⚠️  Exception: {e}")

if __name__ == "__main__":
    test_malformed_packets()
