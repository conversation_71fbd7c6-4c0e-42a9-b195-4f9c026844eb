#!/usr/bin/env python3
import socket
import struct
import time

def create_dns_query(domain, query_id=0x1234):
    """Create a simple DNS A record query"""
    # Header: ID, Flags, QDCOUNT, ANCOUNT, NSCOUNT, ARCOUNT
    header = struct.pack('!HHHHHH', query_id, 0x0100, 1, 0, 0, 0)
    
    # Question section
    question = b''
    for label in domain.split('.'):
        question += struct.pack('!B', len(label)) + label.encode()
    question += b'\x00'  # End of domain name
    question += struct.pack('!HH', 1, 1)  # Type A, Class IN
    
    return header + question

def parse_dns_response(response):
    """Parse DNS response and extract IP addresses"""
    if len(response) < 12:
        return {"error": "Invalid response"}
    
    # Parse header
    header = struct.unpack('!HHHHHH', response[:12])
    query_id, flags, qdcount, ancount, nscount, arcount = header
    
    # Skip questions section
    offset = 12
    for _ in range(qdcount):
        # Skip domain name
        while offset < len(response) and response[offset] != 0:
            if response[offset] & 0xC0 == 0xC0:  # Compression pointer
                offset += 2
                break
            else:
                offset += response[offset] + 1
        if offset < len(response) and response[offset] == 0:
            offset += 1
        offset += 4  # Skip QTYPE and QCLASS
    
    # Parse answers
    ips = []
    for _ in range(ancount):
        # Skip name
        while offset < len(response) and response[offset] != 0:
            if response[offset] & 0xC0 == 0xC0:  # Compression pointer
                offset += 2
                break
            else:
                offset += response[offset] + 1
        if offset < len(response) and response[offset] == 0:
            offset += 1
        
        if offset + 10 <= len(response):
            rtype, rclass, ttl, rdlength = struct.unpack('!HHIH', response[offset:offset+10])
            offset += 10
            
            if rtype == 1 and rdlength == 4 and offset + 4 <= len(response):  # A record
                ip_bytes = response[offset:offset+4]
                ip = '.'.join(str(b) for b in ip_bytes)
                ips.append(ip)
            offset += rdlength
    
    return {
        "query_id": query_id,
        "answer_count": ancount,
        "ips": ips,
        "rcode": flags & 0xF
    }

def test_authoritative_mode():
    print("🧪 Testing Authoritative Mode (Hardcoded Responses)")
    print("=" * 60)
    
    server_ip = "127.0.0.1"
    server_port = 2053
    
    test_domains = ["google.com", "example.com", "test.local", "any-domain.xyz"]
    
    for domain in test_domains:
        print(f"\n🔍 Testing: {domain}")
        
        try:
            query = create_dns_query(domain)
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)
            
            start_time = time.time()
            sock.sendto(query, (server_ip, server_port))
            response, addr = sock.recvfrom(512)
            end_time = time.time()
            sock.close()
            
            parsed = parse_dns_response(response)
            
            print(f"   ✅ Response in {end_time - start_time:.3f}s")
            print(f"   📊 Answers: {parsed['answer_count']}")
            print(f"   📊 IPs: {', '.join(parsed['ips'])}")
            print(f"   📊 Expected: ******* (hardcoded)")
            
            # Verify hardcoded response
            if parsed['ips'] == ['*******']:
                print("   ✅ Correct hardcoded response")
            else:
                print(f"   ❌ Unexpected response: {parsed['ips']}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n📋 Testing Multi-Question in Authoritative Mode")
    try:
        # Create multi-question query
        query_id = 0x5678
        header = struct.pack('!HHHHHH', query_id, 0x0100, 2, 0, 0, 0)  # 2 questions
        
        # First question: google.com
        q1 = b''
        for label in "google.com".split('.'):
            q1 += struct.pack('!B', len(label)) + label.encode()
        q1 += b'\x00' + struct.pack('!HH', 1, 1)
        
        # Second question: example.com
        q2 = b''
        for label in "example.com".split('.'):
            q2 += struct.pack('!B', len(label)) + label.encode()
        q2 += b'\x00' + struct.pack('!HH', 1, 1)
        
        multi_query = header + q1 + q2
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(5)
        sock.sendto(multi_query, (server_ip, server_port))
        response, addr = sock.recvfrom(1024)
        sock.close()
        
        parsed = parse_dns_response(response)
        print(f"   ✅ Multi-question response")
        print(f"   📊 Answers: {parsed['answer_count']} (expected: 2)")
        print(f"   📊 All IPs: {', '.join(parsed['ips'])}")
        
        if parsed['answer_count'] == 2 and all(ip == '*******' for ip in parsed['ips']):
            print("   ✅ Correct multi-question authoritative response")
        else:
            print("   ❌ Unexpected multi-question response")
            
    except Exception as e:
        print(f"   ❌ Multi-question error: {e}")

if __name__ == "__main__":
    test_authoritative_mode()
